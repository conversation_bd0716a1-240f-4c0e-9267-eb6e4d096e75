import React, { useState } from 'react';

const BackendTest = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState(false);

  const testEndpoint = async (name, url, requiresAuth = false) => {
    try {
      const headers = {
        'Content-Type': 'application/json'
      };

      if (requiresAuth) {
        const token = localStorage.getItem('token');
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
      }

      const response = await fetch(url, { headers });
      const data = await response.json();
      
      setResults(prev => ({
        ...prev,
        [name]: {
          status: response.status,
          ok: response.ok,
          data: data,
          error: null
        }
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [name]: {
          status: 'ERROR',
          ok: false,
          data: null,
          error: error.message
        }
      }));
    }
  };

  const runTests = async () => {
    setLoading(true);
    setResults({});

    const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
    const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

    // Test basic connectivity
    await testEndpoint('Health Check', `${apiUrl}/`);
    
    // Test auth endpoint
    await testEndpoint('Auth Me', `${apiUrl}/api/auth/me`, true);
    
    // Test analytics test endpoint
    await testEndpoint('Analytics Test', `${apiUrl}/api/analytics/client/test`);
    
    // Test analytics overview
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - 7);
    await testEndpoint(
      'Analytics Overview', 
      `${apiUrl}/api/analytics/client/overview?start=${start.toISOString()}&end=${end.toISOString()}`,
      true
    );

    setLoading(false);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-4">Backend Connection Test</h2>
      
      <button
        onClick={runTests}
        disabled={loading}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? 'Testing...' : 'Run Tests'}
      </button>

      <div className="mt-6 space-y-4">
        {Object.entries(results).map(([name, result]) => (
          <div key={name} className="border rounded p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">{name}</h3>
              <span className={`px-2 py-1 rounded text-sm ${
                result.ok ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {result.status}
              </span>
            </div>
            
            {result.error && (
              <div className="text-red-600 text-sm mb-2">
                Error: {result.error}
              </div>
            )}
            
            {result.data && (
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(result.data, null, 2)}
              </pre>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 text-sm text-gray-600">
        <p><strong>API URL:</strong> {process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app'}</p>
        <p><strong>Token Present:</strong> {localStorage.getItem('token') ? 'Yes' : 'No'}</p>
      </div>
    </div>
  );
};

export default BackendTest;
